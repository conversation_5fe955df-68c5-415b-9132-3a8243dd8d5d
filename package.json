{"name": "<PERSON>lo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ai": "jest tests/ai/", "test:api": "jest tests/api/", "test:database": "jest tests/database/", "test:e2e": "jest tests/e2e/", "test:integration": "jest tests/integration/", "test:performance": "jest tests/performance/", "test:ci": "jest --coverage --watchAll=false --maxWorkers=2", "test:debug": "jest --no-coverage --detectOpenHandles --verbose", "validate:dashboard": "node scripts/validate-dashboard-data.js", "test:dashboard": "node scripts/run-dashboard-tests.js", "validate:management": "node scripts/validate-management-pages.js", "diagnose:management": "node scripts/diagnose-management-pages.js", "test:management-apis": "node scripts/test-management-apis.js", "validate:rls": "node scripts/validate-rls-fix.js", "debug:api-calls": "node scripts/debug-api-calls.js", "validate:api-fixes": "node scripts/validate-api-fixes.js", "validate:navigation": "node scripts/validate-navigation-paths.js", "validate:dashboard-data": "node scripts/validate-dashboard-data-quality.js", "validate:cross-role": "node scripts/validate-cross-role-dashboard.js", "validate:all": "npm run validate:dashboard && npm run test:dashboard && npm run validate:management && npm run validate:rls && npm run validate:navigation && npm run validate:dashboard-data && npm run validate:cross-role", "prepare": "husky install"}, "dependencies": {"@ai-sdk/openai": "^0.0.66", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.39.0", "ai": "^3.0.0", "dotenv": "^16.5.0", "lucide-react": "^0.511.0", "next": "14.2.29", "openai": "^4.28.0", "react": "^18", "react-dom": "^18", "react-qr-code": "^2.0.15", "zod": "^3.25.28"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "14.2.29", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "prettier": "^3.0.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}