/**
 * WhatsApp QR Code API Endpoint
 * 
 * Provides QR code data for WhatsApp instance connection with auto-refresh support.
 * Integrates with Evolution API v2 and webhook-based QR code delivery.
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-28
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { WhatsAppChannelService } from '@/lib/channels/whatsapp/WhatsAppChannelService';
import { z } from 'zod';

// =====================================================
// VALIDATION SCHEMAS
// =====================================================

const QRCodeRequestSchema = z.object({
  id: z.string().uuid('Invalid instance ID format')
});

// =====================================================
// API ROUTE HANDLERS
// =====================================================

/**
 * GET /api/channels/whatsapp/instances/[id]/qr
 * 
 * @description Retrieves QR code for WhatsApp instance connection.
 * Supports auto-refresh with webhook-based updates.
 * 
 * @param request - Next.js request object
 * @param params - Route parameters containing instance ID
 * @returns JSON response with QR code data or error
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient();

    // =====================================================
    // AUTHENTICATION & AUTHORIZATION
    // =====================================================

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }

    // Get user profile for RBAC
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({
        success: false,
        error: 'User profile not found'
      }, { status: 403 });
    }

    // =====================================================
    // INPUT VALIDATION
    // =====================================================

    const validation = QRCodeRequestSchema.safeParse({ id: params.id });
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid request parameters',
        details: validation.error.errors
      }, { status: 400 });
    }

    const { id: instanceId } = validation.data;

    // =====================================================
    // INSTANCE VERIFICATION
    // =====================================================

    // Get instance and verify ownership/access
    const { data: instance, error: instanceError } = await supabase
      .from('channel_instances')
      .select('*')
      .eq('id', instanceId)
      .eq('channel_type', 'whatsapp')
      .single();

    if (instanceError || !instance) {
      return NextResponse.json({
        success: false,
        error: 'WhatsApp instance not found'
      }, { status: 404 });
    }

    // RBAC: Check if user has access to this instance
    const hasAccess = 
      profile.role === 'superadmin' || // Superadmin has access to all
      instance.organization_id === profile.organization_id; // Same organization

    if (!hasAccess) {
      return NextResponse.json({
        success: false,
        error: 'Access denied to this instance'
      }, { status: 403 });
    }

    // =====================================================
    // STATUS CHECKS
    // =====================================================

    // Check if instance is already connected
    if (instance.status === 'connected') {
      return NextResponse.json({
        success: true,
        status: 'connected',
        message: 'Instance is already connected',
        data: {
          instanceId: instance.id,
          instanceName: instance.instance_name,
          status: 'connected',
          connectedAt: instance.updated_at
        }
      });
    }

    // Check if instance is in error state
    if (instance.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Instance is in error state',
        details: {
          status: instance.status,
          lastError: instance.config?.last_error || 'Unknown error'
        }
      }, { status: 409 });
    }

    // =====================================================
    // QR CODE RETRIEVAL
    // =====================================================

    try {
      // Initialize WhatsApp service
      const whatsappService = new WhatsAppChannelService(supabase, instance.organization_id);

      // Get QR code from service
      const qrResult = await whatsappService.getQRCode(instanceId);

      if (!qrResult.qrCode) {
        // No QR code available yet, but instance is connecting
        return NextResponse.json({
          success: true,
          status: 'loading',
          message: 'QR code is being generated',
          data: {
            instanceId: instance.id,
            instanceName: instance.instance_name,
            status: 'loading',
            qrCode: null,
            expiresAt: null,
            lastUpdated: new Date().toISOString()
          }
        });
      }

      // QR code is available
      const expiresAt = new Date(Date.now() + 45000).toISOString(); // 45 seconds from now

      return NextResponse.json({
        success: true,
        status: 'available',
        message: 'QR code retrieved successfully',
        data: {
          instanceId: instance.id,
          instanceName: instance.instance_name,
          status: 'available',
          qrCode: qrResult.qrCode,
          expiresAt,
          lastUpdated: new Date().toISOString()
        }
      });

    } catch (qrError) {
      console.error('Error retrieving QR code:', qrError);

      // Handle specific Evolution API errors
      if (qrError instanceof Error) {
        if (qrError.message.includes('not found')) {
          return NextResponse.json({
            success: false,
            error: 'Evolution API instance not found',
            details: {
              message: 'The WhatsApp instance was not found in Evolution API. It may need to be recreated.',
              suggestion: 'Try disconnecting and reconnecting the instance.'
            }
          }, { status: 404 });
        }

        if (qrError.message.includes('already connected')) {
          return NextResponse.json({
            success: true,
            status: 'connected',
            message: 'Instance is already connected',
            data: {
              instanceId: instance.id,
              instanceName: instance.instance_name,
              status: 'connected',
              connectedAt: new Date().toISOString()
            }
          });
        }
      }

      return NextResponse.json({
        success: false,
        error: 'Failed to retrieve QR code',
        details: {
          message: qrError instanceof Error ? qrError.message : 'Unknown error',
          instanceId: instance.id,
          timestamp: new Date().toISOString()
        }
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Unexpected error in QR code endpoint:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// =====================================================
// WEBHOOK INTEGRATION SUPPORT
// =====================================================

/**
 * POST /api/channels/whatsapp/instances/[id]/qr
 * 
 * @description Manually trigger QR code refresh for testing purposes.
 * Only available in development mode.
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json({
      success: false,
      error: 'Manual QR refresh only available in development mode'
    }, { status: 403 });
  }

  try {
    const supabase = await createClient();

    // Basic auth check
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }

    // Validate instance ID
    const validation = QRCodeRequestSchema.safeParse({ id: params.id });
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid instance ID'
      }, { status: 400 });
    }

    // Force refresh by calling GET endpoint
    const getResponse = await GET(request, { params });
    return getResponse;

  } catch (error) {
    console.error('Error in manual QR refresh:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to refresh QR code'
    }, { status: 500 });
  }
}
