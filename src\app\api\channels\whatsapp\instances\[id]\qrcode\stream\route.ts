/**
 * WhatsApp QR Code Real-time Streaming Endpoint
 * 
 * Provides Server-Sent Events (SSE) for real-time QR code updates
 * from Evolution API webhooks.
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-28
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { createEvolutionAPIService } from '@/lib/services/EvolutionAPIService';

// =====================================================
// TYPES AND INTERFACES
// =====================================================

interface QRCodeStreamEvent {
  type: 'qr_code' | 'status_update' | 'error' | 'heartbeat';
  data: {
    instanceId: string;
    qrCode?: string;
    status?: string;
    message?: string;
    timestamp: string;
    expiresAt?: string;
    isRealQR?: boolean;
    source?: 'evolution_api' | 'database' | 'mock';
  };
}

// =====================================================
// SSE STREAMING ENDPOINT
// =====================================================

/**
 * Stream QR code updates via Server-Sent Events
 * 
 * @description Provides real-time QR code updates for WhatsApp instance
 * connection. Clients can listen to this stream to receive QR codes
 * as soon as they're available from Evolution API webhooks.
 * 
 * @param request - Next.js request object
 * @param params - Route parameters containing instance ID
 * @returns SSE stream with QR code updates
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const instanceId = params.id;
  
  // Create a readable stream for SSE
  const stream = new ReadableStream({
    start(controller) {
      let isActive = true;
      let heartbeatInterval: NodeJS.Timeout;
      let pollInterval: NodeJS.Timeout;

      // SSE helper function
      const sendEvent = (event: QRCodeStreamEvent) => {
        if (!isActive) return;

        try {
          const data = `data: ${JSON.stringify(event)}\n\n`;
          controller.enqueue(new TextEncoder().encode(data));

          // Enhanced logging for QR code events
          if (event.type === 'qr_code') {
            console.log('📱 QR Code event sent:', {
              instanceId: event.data.instanceId,
              hasQRCode: !!event.data.qrCode,
              qrCodeLength: event.data.qrCode?.length || 0,
              source: event.data.source,
              isRealQR: event.data.isRealQR,
              expiresAt: event.data.expiresAt
            });
          }
        } catch (error) {
          console.error('❌ Error sending SSE event:', error);
          isActive = false;
        }
      };

      // Function to get real QR code from Evolution API
      const getRealQRCode = async (instanceName: string): Promise<{ qrCode: string; source: string } | null> => {
        try {
          console.log('🔍 Attempting to get real QR code from Evolution API for instance:', instanceName);

          const evolutionAPI = createEvolutionAPIService();
          const qrResponse = await evolutionAPI.getQRCode(instanceName);

          if (qrResponse?.base64) {
            console.log('✅ Successfully obtained real QR code from Evolution API');
            return {
              qrCode: qrResponse.base64,
              source: 'evolution_api'
            };
          } else {
            console.warn('⚠️ Evolution API returned empty QR code');
            return null;
          }
        } catch (error) {
          console.error('❌ Error getting real QR code from Evolution API:', error);
          return null;
        }
      };

      // Function to validate QR code base64
      const validateQRCode = (base64: string): boolean => {
        try {
          // Check if it's a valid base64 string
          if (!base64 || typeof base64 !== 'string') return false;

          // Check minimum length (real QR codes are much longer than 1x1 pixel)
          if (base64.length < 100) {
            console.warn('⚠️ QR code too short, likely invalid:', base64.length);
            return false;
          }

          // Try to decode base64 to verify it's valid
          const decoded = atob(base64);
          if (decoded.length < 50) {
            console.warn('⚠️ Decoded QR code too small, likely invalid');
            return false;
          }

          return true;
        } catch (error) {
          console.error('❌ Invalid base64 QR code:', error);
          return false;
        }
      };

      // Function to generate a mock QR code for development fallback
      const generateMockQRCode = (): string => {
        // Generate a realistic-looking QR code base64 for development
        // This is a 200x200 pixel QR code with WhatsApp-like data
        const mockQRData = `2@${Math.random().toString(36).substring(2, 15)}@${Date.now()}@WhatsApp`;

        // Create a simple QR code pattern (this would normally be generated by a QR library)
        // For development purposes, we'll create a base64 that represents a valid PNG
        const canvas = typeof document !== 'undefined' ? document.createElement('canvas') : null;

        if (canvas) {
          canvas.width = 200;
          canvas.height = 200;
          const ctx = canvas.getContext('2d');
          if (ctx) {
            // Create a simple pattern that looks like a QR code
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, 200, 200);
            ctx.fillStyle = '#FFFFFF';

            // Create QR-like pattern
            for (let i = 0; i < 200; i += 10) {
              for (let j = 0; j < 200; j += 10) {
                if ((i + j) % 20 === 0) {
                  ctx.fillRect(i, j, 8, 8);
                }
              }
            }

            return canvas.toDataURL('image/png').split(',')[1];
          }
        }

        // Fallback: Return a base64 string that represents a simple black square PNG
        // This is a minimal valid PNG file encoded in base64
        return 'iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAAdgAAAHYBTnsmCAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAFYSURBVBiVY/z//z8DJQAggBhJVQcQQIykqgMIIEZS1QEEECO56gACiJFcdQABxEiuOoAAYiRXHUAAMZKrDiCAGMlVBxBAjOSqAwggRnLVAQQQI7nqAAKIkVx1AAHESKo6gABiJFUdQAAxkqoOIIAYSVUHEECMpKoDCCBGUtUBBBAjqeoAAoiRVHUAAcRIqjqAAGIkVR1AADGSLA4ggBhJVQcQQIykqgMIIEZS1QEEECO56gACiJFcdQABxEiuOoAAYiRXHUAAMZKrDiCAGMlVBxBAjOSqAwggRnLVAQQQI7nqAAKIkVx1AAHESKo6gABiJFUdQAAxkqoOIIAYSVUHEECMpKoDCCBGUtUBBBAjqeoAAoiRVHUAAcRIqjqAAGIkVR1AADGSLA4ggBhJVQcQQIykqgMIIEZS1QEEECO56gACiJFcdQABxEiuOoAAYiRXHUAAMZKrDiCAGAEAF2Af8ANd6m4AAAAASUVORK5CYII='.repeat(20);
      };

      // Initialize connection
      const initializeStream = async () => {
        try {
          const supabase = await createClient();

          // Authenticate user
          const { data: { user }, error: authError } = await supabase.auth.getUser();
          if (authError || !user) {
            sendEvent({
              type: 'error',
              data: {
                instanceId,
                message: 'Authentication required',
                timestamp: new Date().toISOString()
              }
            });
            controller.close();
            return;
          }

          // Enhanced development mode with real QR codes
          const isDevelopment = process.env.NODE_ENV === 'development';
          if (isDevelopment) {
            console.log('🔧 Development mode: Creating real Evolution API instance for QR code');

            // Send initial status
            sendEvent({
              type: 'status_update',
              data: {
                instanceId,
                status: 'connecting',
                message: 'Connected to QR code stream (development mode)',
                timestamp: new Date().toISOString()
              }
            });

            // Create a real instance in Evolution API for development
            try {
              const evolutionAPI = createEvolutionAPIService();
              // Use timestamp to ensure fresh instance for testing our qrcode: true fix
              const devInstanceName = `agentsalud-test-${Date.now()}`;

              console.log(`🔧 Creating real Evolution API instance: ${devInstanceName}`);

              // Create instance
              const instanceResponse = await evolutionAPI.createInstance({
                instanceName: devInstanceName,
                integration: 'WHATSAPP-BAILEYS'
              });

              if (instanceResponse?.instance?.instanceId) {
                console.log('✅ Development instance created successfully');

                // Check if QR code is available immediately in creation response
                if (instanceResponse.qrCodeFromCreation?.base64) {
                  console.log('🎯 QR code available immediately from creation response!');
                  sendEvent({
                    type: 'qr_code',
                    data: {
                      instanceId,
                      qrCode: instanceResponse.qrCodeFromCreation.base64,
                      expiresAt: new Date(Date.now() + 45000).toISOString(),
                      timestamp: new Date().toISOString(),
                      isRealQR: true,
                      source: 'creation_response',
                      pairingCode: instanceResponse.qrCodeFromCreation.pairingCode,
                      count: instanceResponse.qrCodeFromCreation.count
                    }
                  });

                  // Store the instance name for potential future operations
                  (global as any).devInstanceName = devInstanceName;
                  return; // QR code sent, no need for polling
                }

                // Fallback: Wait a moment for instance to initialize
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Connect the instance to start WhatsApp connection process
                console.log('🔗 Connecting instance to start WhatsApp connection...');
                try {
                  await evolutionAPI.connectInstance(devInstanceName);
                  console.log('✅ Instance connection initiated successfully');

                  // Send status that we're generating QR
                  sendEvent({
                    type: 'status_update',
                    data: {
                      instanceId,
                      status: 'qr_generating',
                      message: 'Conectando a WhatsApp y generando código QR...',
                      timestamp: new Date().toISOString()
                    }
                  });

                } catch (connectError) {
                  console.error('❌ Failed to connect instance:', connectError);
                  sendEvent({
                    type: 'status_update',
                    data: {
                      instanceId,
                      status: 'connection_error',
                      message: 'Error al conectar la instancia. Reintentando...',
                      timestamp: new Date().toISOString()
                    }
                  });
                }

                // The polling mechanism will handle getting the QR code
                // Store the instance name for polling
                (global as any).devInstanceName = devInstanceName;
              } else {
                throw new Error('Failed to create development instance');
              }

            } catch (devError) {
              console.error('❌ Development mode: Failed to create real instance:', devError);

              // Fallback to mock QR code for development
              const mockQRCode = generateMockQRCode();
              sendEvent({
                type: 'qr_code',
                data: {
                  instanceId,
                  qrCode: mockQRCode,
                  expiresAt: new Date(Date.now() + 45000).toISOString(),
                  timestamp: new Date().toISOString(),
                  isRealQR: false,
                  source: 'mock'
                }
              });
            }

            // Continue with normal flow for testing but don't fail on missing instance
          }

          // Get user profile and verify access
          const { data: profile } = await supabase
            .from('profiles')
            .select('organization_id, role')
            .eq('id', user.id)
            .single();

          if (!profile || !['admin', 'superadmin'].includes(profile.role)) {
            if (!isDevelopment) {
              sendEvent({
                type: 'error',
                data: {
                  instanceId,
                  message: 'Admin access required',
                  timestamp: new Date().toISOString()
                }
              });
              controller.close();
              return;
            } else {
              console.log('🔧 Development mode: Continuing despite missing admin role');
            }
          }

          // Verify instance access
          const { data: instance } = await supabase
            .from('channel_instances')
            .select('*')
            .eq('id', instanceId)
            .eq('organization_id', profile?.organization_id || 'dev-org')
            .single();

          if (!instance) {
            if (!isDevelopment) {
              sendEvent({
                type: 'error',
                data: {
                  instanceId,
                  message: 'Instance not found or access denied',
                  timestamp: new Date().toISOString()
                }
              });
              controller.close();
              return;
            } else {
              console.log('🔧 Development mode: Continuing despite missing instance');
              // In development mode, continue with mock data but skip real instance operations
              // Don't return early - we still need to set up polling and heartbeat for the stream
            }
          }

          // Send initial status (only for production with real instance)
          if (!isDevelopment && instance) {
            sendEvent({
              type: 'status_update',
              data: {
                instanceId,
                status: instance.status,
                message: 'Connected to QR code stream',
                timestamp: new Date().toISOString()
              }
            });

            // Check for existing QR code with validation
            const currentQR = instance.config?.whatsapp?.qr_code?.current_qr;
            const expiresAt = instance.config?.whatsapp?.qr_code?.expires_at;

            if (currentQR && expiresAt && new Date(expiresAt) > new Date()) {
              if (validateQRCode(currentQR)) {
                console.log('✅ Found valid existing QR code in database');
                sendEvent({
                  type: 'qr_code',
                  data: {
                    instanceId,
                    qrCode: currentQR,
                    expiresAt,
                    timestamp: new Date().toISOString(),
                    isRealQR: true,
                    source: 'database'
                  }
                });
              } else {
                console.warn('⚠️ Existing QR code in database is invalid, will request new one');
              }
            }
          }

          // Set up polling for QR code updates
          if (!isDevelopment) {
            // Production polling - check database for updates
            pollInterval = setInterval(async () => {
              if (!isActive) {
                clearInterval(pollInterval);
                return;
              }

              try {
                const { data: updatedInstance } = await supabase
                  .from('channel_instances')
                  .select('config, status')
                  .eq('id', instanceId)
                  .single();

                if (updatedInstance) {
                  const qrCode = updatedInstance.config?.whatsapp?.qr_code?.current_qr;
                  const qrExpiresAt = updatedInstance.config?.whatsapp?.qr_code?.expires_at;
                  const lastUpdated = updatedInstance.config?.whatsapp?.qr_code?.last_updated;

                  // Check if QR code is new or updated with validation
                  if (qrCode && qrExpiresAt && new Date(qrExpiresAt) > new Date()) {
                    if (validateQRCode(qrCode)) {
                      console.log('✅ Found valid updated QR code in database');
                      sendEvent({
                        type: 'qr_code',
                        data: {
                          instanceId,
                          qrCode,
                          expiresAt: qrExpiresAt,
                          timestamp: lastUpdated || new Date().toISOString(),
                          isRealQR: true,
                          source: 'database'
                        }
                      });
                    } else {
                      console.warn('⚠️ Updated QR code in database is invalid');
                      sendEvent({
                        type: 'error',
                        data: {
                          instanceId,
                          message: 'QR code inválido detectado, solicitando nuevo código',
                          timestamp: new Date().toISOString()
                        }
                      });
                    }
                  }

                  // Send status updates
                  if (instance && updatedInstance.status !== instance.status) {
                    sendEvent({
                      type: 'status_update',
                      data: {
                        instanceId,
                        status: updatedInstance.status,
                        timestamp: new Date().toISOString()
                      }
                    });
                  }
                }
              } catch (pollError) {
                console.error('❌ Error polling for QR code updates:', pollError);
              }
            }, 2000); // Poll every 2 seconds
          } else {
            // Development polling - retry getting real QR codes
            console.log('🔧 Development mode: Setting up QR code retry polling');
            let retryCount = 0;
            const maxRetries = 6; // Increased retries for better success rate

            pollInterval = setInterval(async () => {
              if (!isActive || retryCount >= maxRetries) {
                if (retryCount >= maxRetries) {
                  console.log('🔧 Development mode: Max retries reached, using mock QR code');
                  const mockQRCode = generateMockQRCode();
                  sendEvent({
                    type: 'qr_code',
                    data: {
                      instanceId,
                      qrCode: mockQRCode,
                      expiresAt: new Date(Date.now() + 45000).toISOString(),
                      timestamp: new Date().toISOString(),
                      isRealQR: false,
                      source: 'mock'
                    }
                  });
                }
                clearInterval(pollInterval);
                return;
              }

              retryCount++;
              console.log(`🔧 Development mode: QR retry attempt ${retryCount}/${maxRetries}`);

              try {
                // Use the stored instance name or fallback
                const devInstanceName = (global as any).devInstanceName || `agentsalud-dev-${instanceId.substring(0, 8)}`;
                const realQR = await getRealQRCode(devInstanceName);

                if (realQR && validateQRCode(realQR.qrCode)) {
                  console.log('✅ Development mode: Got real QR code on retry');
                  sendEvent({
                    type: 'qr_code',
                    data: {
                      instanceId,
                      qrCode: realQR.qrCode,
                      expiresAt: new Date(Date.now() + 45000).toISOString(),
                      timestamp: new Date().toISOString(),
                      isRealQR: true,
                      source: realQR.source
                    }
                  });
                  clearInterval(pollInterval); // Stop retrying once we get a QR code
                } else {
                  console.log(`⚠️ Development mode: QR not ready yet (attempt ${retryCount}/${maxRetries})`);
                }
              } catch (retryError) {
                console.log(`⚠️ Development mode: Retry ${retryCount} failed:`, retryError.message);
              }
            }, 3000); // Retry every 3 seconds
          }

          // Set up heartbeat
          heartbeatInterval = setInterval(() => {
            if (!isActive) {
              clearInterval(heartbeatInterval);
              return;
            }
            
            sendEvent({
              type: 'heartbeat',
              data: {
                instanceId,
                timestamp: new Date().toISOString()
              }
            });
          }, 30000); // Heartbeat every 30 seconds

        } catch (error) {
          console.error('❌ Error initializing QR code stream:', error);
          sendEvent({
            type: 'error',
            data: {
              instanceId,
              message: 'Failed to initialize stream',
              timestamp: new Date().toISOString()
            }
          });
          controller.close();
        }
      };

      // Handle stream cancellation
      const cleanup = () => {
        console.log('🧹 Cleaning up QR code stream for instance:', instanceId);
        isActive = false;
        if (heartbeatInterval) {
          clearInterval(heartbeatInterval);
          heartbeatInterval = undefined;
        }
        if (pollInterval) {
          clearInterval(pollInterval);
          pollInterval = undefined;
        }
        try {
          controller.close();
        } catch (error) {
          // Controller might already be closed
          console.log('ℹ️ Stream controller already closed');
        }
      };

      // Initialize the stream
      initializeStream();

      // Return cleanup function
      return cleanup;
    },

    cancel() {
      console.log('🔌 QR code stream cancelled for instance:', instanceId);
    }
  });

  // Return SSE response
  return new NextResponse(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Cache-Control'
    }
  });
}
