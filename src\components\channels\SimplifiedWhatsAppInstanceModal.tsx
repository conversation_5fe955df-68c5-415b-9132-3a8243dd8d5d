/**
 * Simplified WhatsApp Instance Creation Modal
 * 
 * Implements the two-step WhatsApp instance flow:
 * Step 1: Create instance with basic info (disconnected state)
 * Step 2: Connect via QR code (separate action)
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-28
 */

'use client';

import React, { useState } from 'react';
import { X, MessageSquare, Loader2, CheckCircle } from 'lucide-react';
import { extractWhatsAppErrorMessage } from '@/utils/errorHandling';
// Using browser alert for now - can be replaced with proper toast system later

// =====================================================
// TYPES AND INTERFACES
// =====================================================

interface SimplifiedWhatsAppInstanceModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Function to close the modal */
  onClose: () => void;
  /** Function called when instance is created successfully */
  onInstanceCreated: (instanceId: string) => void;
  /** Organization ID for the instance */
  organizationId: string;
  /** User role for automatic naming */
  userRole?: 'admin' | 'superadmin';
  /** Organization name for automatic naming */
  organizationName?: string;
}

interface InstanceFormData {
  instanceName: string;
}

// =====================================================
// MAIN COMPONENT
// =====================================================

/**
 * SimplifiedWhatsAppInstanceModal - Two-step instance creation
 * 
 * @description Creates WhatsApp instances in disconnected state first,
 * then allows connection via separate QR code flow.
 * 
 * @param props - Component props
 * @returns JSX element
 */
export const SimplifiedWhatsAppInstanceModal: React.FC<SimplifiedWhatsAppInstanceModalProps> = ({
  isOpen,
  onClose,
  onInstanceCreated,
  organizationId,
  userRole = 'admin',
  organizationName
}) => {
  // =====================================================
  // STATE MANAGEMENT
  // =====================================================

  // Generate automatic instance name for tenant users
  const generateAutomaticInstanceName = (): string => {
    const timestamp = Date.now();
    const orgName = organizationName || 'Organización';
    const cleanOrgName = orgName
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 15);

    return `${cleanOrgName}-WhatsApp-${timestamp}`;
  };

  const [formData, setFormData] = useState<InstanceFormData>({
    instanceName: userRole === 'admin' ? generateAutomaticInstanceName() : ''
  });
  const [isCreating, setIsCreating] = useState(false);
  const [step, setStep] = useState<'form' | 'creating' | 'success'>('form');
  const [createdInstanceId, setCreatedInstanceId] = useState<string | null>(null);

  // =====================================================
  // UTILITY FUNCTIONS
  // =====================================================

  // Using centralized error handling from @/utils/errorHandling

  // =====================================================
  // EVENT HANDLERS
  // =====================================================

  /**
   * Handle form input changes
   */
  const handleInputChange = (field: keyof InstanceFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  /**
   * Handle form submission - Create instance in disconnected state
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.instanceName.trim()) {
      const errorMessage = 'El nombre de la instancia es requerido';
      console.warn('⚠️ Validation error:', errorMessage);
      alert(errorMessage);
      return;
    }

    setIsCreating(true);
    setStep('creating');

    try {
      console.log('🚀 Creating WhatsApp instance with name:', formData.instanceName.trim());

      // Create instance with simplified configuration (disconnected state)
      const response = await fetch('/api/channels/whatsapp/instances', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          instance_name: formData.instanceName.trim(),
          phone_number: '', // Empty for two-step flow
          skipConnection: true // Enable two-step flow
        })
      });

      // Handle HTTP error responses
      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (parseError) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const errorMessage = extractWhatsAppErrorMessage(errorData);
        console.error('❌ API Error Response:', errorData);
        throw new Error(errorMessage);
      }

      // Parse successful response
      const result = await response.json();
      console.log('📋 API Response:', result);

      // Handle successful response
      if (result.success && result.data?.instance?.id) {
        setCreatedInstanceId(result.data.instance.id);
        setStep('success');
        console.log('✅ Instancia de WhatsApp creada exitosamente:', result.data.instance.id);
      } else {
        // Handle API success=false responses
        const errorMessage = extractWhatsAppErrorMessage(result);
        console.error('❌ API returned success=false:', result);
        throw new Error(errorMessage);
      }

    } catch (error) {
      console.error('❌ Error creating WhatsApp instance:', error);
      const errorMessage = extractWhatsAppErrorMessage(error);
      alert(errorMessage);
      setStep('form');
    } finally {
      setIsCreating(false);
    }
  };

  /**
   * Handle modal close
   */
  const handleClose = () => {
    if (isCreating) return; // Prevent closing while creating
    
    // Reset form state
    setFormData({ instanceName: '' });
    setStep('form');
    setCreatedInstanceId(null);
    onClose();
  };

  /**
   * Handle success completion
   */
  const handleComplete = () => {
    if (createdInstanceId) {
      onInstanceCreated(createdInstanceId);
    }
    handleClose();
  };

  // =====================================================
  // RENDER HELPERS
  // =====================================================

  /**
   * Render form step
   */
  const renderFormStep = () => (
    <div>
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Crear Instancia de WhatsApp
        </h3>
        <p className="text-sm text-gray-600">
          Crea una nueva instancia de WhatsApp. Podrás conectarla después usando un código QR.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Show instance name input only for superadmin users */}
        {userRole === 'superadmin' && (
          <div>
            <label htmlFor="instanceName" className="block text-sm font-medium text-gray-700 mb-1">
              Nombre de la Instancia *
            </label>
            <input
              type="text"
              id="instanceName"
              value={formData.instanceName}
              onChange={(e) => handleInputChange('instanceName', e.target.value)}
              placeholder="Ej: WhatsApp Principal"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
              disabled={isCreating}
            />
            <p className="text-xs text-gray-500 mt-1">
              Este nombre te ayudará a identificar la instancia en el panel de administración.
            </p>
          </div>
        )}

        {/* Show automatic naming info for tenant admin users */}
        {userRole === 'admin' && (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div className="flex items-start">
              <MessageSquare className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-blue-900 mb-1">
                  Configuración Automática
                </h4>
                <p className="text-sm text-blue-700 mb-2">
                  Se creará automáticamente una instancia de WhatsApp para tu organización.
                </p>
                <p className="text-xs text-blue-600">
                  Nombre: <span className="font-mono bg-blue-100 px-1 rounded">{formData.instanceName}</span>
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={handleClose}
            disabled={isCreating}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
          >
            Cancelar
          </button>
          <button
            type="submit"
            disabled={isCreating || !formData.instanceName.trim()}
            className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isCreating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creando...
              </>
            ) : (
              <>
                <MessageSquare className="h-4 w-4 mr-2" />
                Crear Instancia
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );

  /**
   * Render creating step
   */
  const renderCreatingStep = () => (
    <div className="text-center py-8">
      <Loader2 className="h-12 w-12 mx-auto text-blue-600 animate-spin mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        Creando Instancia de WhatsApp
      </h3>
      <p className="text-sm text-gray-600">
        Por favor espera mientras configuramos tu nueva instancia...
      </p>
    </div>
  );

  /**
   * Render success step
   */
  const renderSuccessStep = () => (
    <div className="text-center py-8">
      <CheckCircle className="h-12 w-12 mx-auto text-green-600 mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        ¡Instancia Creada Exitosamente!
      </h3>
      <p className="text-sm text-gray-600 mb-6">
        Tu instancia de WhatsApp ha sido creada. Ahora puedes conectarla usando el botón "Conectar" en la tarjeta de la instancia.
      </p>
      <button
        type="button"
        onClick={handleComplete}
        className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700"
      >
        Continuar
      </button>
    </div>
  );

  // =====================================================
  // MAIN RENDER
  // =====================================================

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={handleClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          {/* Header */}
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <MessageSquare className="h-6 w-6 text-green-600 mr-2" />
                <span className="text-lg font-medium text-gray-900">WhatsApp</span>
              </div>
              <button
                type="button"
                onClick={handleClose}
                disabled={isCreating}
                className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
                title="Cerrar modal"
                aria-label="Cerrar modal"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Content based on step */}
            {step === 'form' && renderFormStep()}
            {step === 'creating' && renderCreatingStep()}
            {step === 'success' && renderSuccessStep()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimplifiedWhatsAppInstanceModal;
