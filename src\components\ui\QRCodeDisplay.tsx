/**
 * QRCodeDisplay Component
 * 
 * A robust QR code display component that handles both development and production modes.
 * Uses react-qr-code library for reliable QR code generation and display.
 * 
 * Features:
 * - Real QR code generation for development mode
 * - Base64 image display for production mode
 * - Comprehensive error handling and fallbacks
 * - Debug logging for troubleshooting
 * - Responsive design with proper styling
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-28
 */

'use client';

import React, { useState, useEffect } from 'react';
import QRCode from 'react-qr-code';
import { QrCode, AlertCircle, RefreshCw } from 'lucide-react';

// =====================================================
// INTERFACES
// =====================================================

interface QRCodeDisplayProps {
  /** QR code data object */
  qrData: {
    code: string;
    base64?: string;
    expiresAt: Date;
  } | null;
  /** Whether we're in development mode */
  isDevelopment?: boolean;
  /** Size of the QR code in pixels */
  size?: number;
  /** Additional CSS classes */
  className?: string;
  /** Callback when QR code needs refresh */
  onRefresh?: () => void;
  /** Show refresh button */
  showRefreshButton?: boolean;
}

// =====================================================
// COMPONENT
// =====================================================

/**
 * QRCodeDisplay Component
 * 
 * Displays QR codes with robust error handling and development mode support.
 */
export default function QRCodeDisplay({
  qrData,
  isDevelopment = process.env.NODE_ENV === 'development',
  size = 192, // 48 * 4 = 192px (w-48 h-48)
  className = '',
  onRefresh,
  showRefreshButton = true
}: QRCodeDisplayProps) {
  const [imageError, setImageError] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  // Debug logging
  useEffect(() => {
    if (isDevelopment && qrData) {
      const debug = {
        hasCode: !!qrData.code,
        hasBase64: !!qrData.base64,
        codeLength: qrData.code?.length || 0,
        base64Length: qrData.base64?.length || 0,
        expiresAt: qrData.expiresAt,
        isExpired: qrData.expiresAt < new Date(),
        isDevelopmentMode: isDevelopment,
        timestamp: new Date().toISOString()
      };
      
      setDebugInfo(debug);
      console.log('🔍 QRCodeDisplay Debug Info:', debug);
    }
  }, [qrData, isDevelopment]);

  // Reset image error when qrData changes
  useEffect(() => {
    setImageError(false);
  }, [qrData]);

  /**
   * Handle image load error
   */
  const handleImageError = () => {
    console.error('❌ QR Code image failed to load:', {
      base64Length: qrData?.base64?.length,
      base64Preview: qrData?.base64?.substring(0, 50) + '...'
    });
    setImageError(true);
  };

  /**
   * Generate development QR code data
   */
  const getDevelopmentQRData = () => {
    const mockData = {
      instanceId: 'dev-instance-' + Date.now(),
      timestamp: new Date().toISOString(),
      mode: 'development',
      message: 'WhatsApp Development QR Code',
      url: 'https://wa.me/qr/mock-development-code'
    };
    
    return JSON.stringify(mockData, null, 2);
  };

  /**
   * Render loading state
   */
  const renderLoading = () => (
    <div className={`bg-gray-100 rounded-lg flex items-center justify-center ${className}`} 
         style={{ width: size, height: size }}>
      <div className="text-center">
        <RefreshCw className="h-8 w-8 text-gray-400 mx-auto mb-2 animate-spin" />
        <p className="text-sm text-gray-500">Generando QR...</p>
      </div>
    </div>
  );

  /**
   * Render error state
   */
  const renderError = () => (
    <div className={`bg-red-50 border-2 border-red-200 rounded-lg flex items-center justify-center ${className}`} 
         style={{ width: size, height: size }}>
      <div className="text-center p-4">
        <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
        <p className="text-sm text-red-600 mb-2">Error al cargar QR</p>
        {showRefreshButton && onRefresh && (
          <button
            onClick={onRefresh}
            className="text-xs bg-red-100 hover:bg-red-200 text-red-700 px-2 py-1 rounded"
          >
            Reintentar
          </button>
        )}
      </div>
    </div>
  );

  /**
   * Render development QR code
   */
  const renderDevelopmentQR = () => {
    const qrValue = getDevelopmentQRData();
    
    return (
      <div className={`bg-white p-4 rounded-lg border-2 border-green-200 ${className}`}>
        <div className="relative">
          <QRCode
            value={qrValue}
            size={size - 32} // Account for padding
            style={{ height: "auto", maxWidth: "100%", width: "100%" }}
            viewBox={`0 0 256 256`}
          />
          {/* Development mode indicator */}
          <div className="absolute top-0 right-0 bg-green-500 text-white text-xs px-2 py-1 rounded-bl-lg">
            DEV
          </div>
        </div>
        <div className="mt-2 text-center">
          <p className="text-xs text-green-600 font-medium">Modo Desarrollo</p>
          <p className="text-xs text-gray-500">QR Code funcional generado</p>
        </div>
      </div>
    );
  };

  /**
   * Render production QR code from base64
   */
  const renderProductionQR = () => {
    if (!qrData?.base64) {
      return renderError();
    }

    if (imageError) {
      return renderError();
    }

    return (
      <div className={`bg-white p-4 rounded-lg border-2 border-blue-200 ${className}`}>
        <img
          src={`data:image/png;base64,${qrData.base64}`}
          alt="WhatsApp QR Code"
          style={{ width: size - 32, height: size - 32 }}
          className="mx-auto"
          onError={handleImageError}
          onLoad={() => console.log('✅ QR Code image loaded successfully')}
        />
        <div className="mt-2 text-center">
          <p className="text-xs text-blue-600 font-medium">Código QR Activo</p>
          <p className="text-xs text-gray-500">
            Expira: {qrData.expiresAt.toLocaleTimeString()}
          </p>
        </div>
      </div>
    );
  };

  /**
   * Render QR code with text fallback
   */
  const renderQRWithTextFallback = () => {
    if (!qrData?.code) {
      return renderError();
    }

    return (
      <div className={`bg-white p-4 rounded-lg border-2 border-purple-200 ${className}`}>
        <QRCode
          value={qrData.code}
          size={size - 32}
          style={{ height: "auto", maxWidth: "100%", width: "100%" }}
          viewBox={`0 0 256 256`}
        />
        <div className="mt-2 text-center">
          <p className="text-xs text-purple-600 font-medium">QR Generado</p>
          <p className="text-xs text-gray-500">Desde código de texto</p>
        </div>
      </div>
    );
  };

  // =====================================================
  // RENDER LOGIC
  // =====================================================

  // No QR data available
  if (!qrData) {
    return renderLoading();
  }

  // Check if QR code is expired
  if (qrData.expiresAt < new Date()) {
    return (
      <div className={`bg-yellow-50 border-2 border-yellow-200 rounded-lg flex items-center justify-center ${className}`} 
           style={{ width: size, height: size }}>
        <div className="text-center p-4">
          <AlertCircle className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
          <p className="text-sm text-yellow-600 mb-2">QR Expirado</p>
          {showRefreshButton && onRefresh && (
            <button
              onClick={onRefresh}
              className="text-xs bg-yellow-100 hover:bg-yellow-200 text-yellow-700 px-2 py-1 rounded"
            >
              Generar Nuevo
            </button>
          )}
        </div>
      </div>
    );
  }

  // Development mode - generate real QR code with mock data
  if (isDevelopment) {
    return renderDevelopmentQR();
  }

  // Production mode - try base64 first, then text fallback
  if (qrData.base64 && !imageError) {
    return renderProductionQR();
  }

  // Fallback to generating QR from text code
  if (qrData.code) {
    return renderQRWithTextFallback();
  }

  // Final fallback
  return renderError();
}

// =====================================================
// DEBUG COMPONENT (Development only)
// =====================================================

/**
 * QRCodeDebugInfo - Shows debug information in development mode
 */
export function QRCodeDebugInfo({ qrData }: { qrData: any }) {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="mt-4 p-3 bg-gray-100 rounded-lg text-xs">
      <h4 className="font-medium text-gray-700 mb-2">Debug Info:</h4>
      <pre className="text-gray-600 overflow-auto">
        {JSON.stringify(qrData, null, 2)}
      </pre>
    </div>
  );
}
