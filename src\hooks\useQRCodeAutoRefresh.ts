/**
 * QR Code Auto-Refresh Hook
 * 
 * Provides real-time QR code updates for WhatsApp instance connection.
 * Implements 30-second auto-refresh with webhook-based delivery and SSE streaming.
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-28
 */

import { useState, useEffect, useCallback, useRef } from 'react';

// =====================================================
// TYPES AND INTERFACES
// =====================================================

export interface QRCodeData {
  qrCode: string | null;
  status: 'loading' | 'available' | 'expired' | 'error' | 'connected';
  expiresAt: string | null;
  lastUpdated: string | null;
  error?: string;
}

export interface QRCodeAutoRefreshOptions {
  instanceId: string;
  autoRefresh?: boolean;
  refreshInterval?: number; // seconds
  maxRetries?: number;
  onStatusChange?: (status: QRCodeData['status']) => void;
  onError?: (error: string) => void;
  onConnected?: () => void;
}

// =====================================================
// QR CODE AUTO-REFRESH HOOK
// =====================================================

/**
 * Hook for managing QR code auto-refresh with real-time updates
 * 
 * @param options - Configuration options for QR code management
 * @returns QR code data and control functions
 */
export function useQRCodeAutoRefresh(options: QRCodeAutoRefreshOptions) {
  const {
    instanceId,
    autoRefresh = true,
    refreshInterval = 30, // 30 seconds default
    maxRetries = 5,
    onStatusChange,
    onError,
    onConnected
  } = options;

  // =====================================================
  // STATE MANAGEMENT
  // =====================================================

  const [qrData, setQrData] = useState<QRCodeData>({
    qrCode: null,
    status: 'loading',
    expiresAt: null,
    lastUpdated: null
  });

  const [isPolling, setIsPolling] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  // =====================================================
  // REFS FOR CLEANUP
  // =====================================================

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // =====================================================
  // QR CODE FETCHING
  // =====================================================

  /**
   * Fetch QR code from API
   */
  const fetchQRCode = useCallback(async (): Promise<QRCodeData> => {
    try {
      // Create abort controller for this request
      const controller = new AbortController();
      abortControllerRef.current = controller;

      const response = await fetch(`/api/channels/whatsapp/instances/${instanceId}/qr`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Instance not found');
        }
        if (response.status === 409) {
          // Instance already connected
          return {
            qrCode: null,
            status: 'connected',
            expiresAt: null,
            lastUpdated: new Date().toISOString()
          };
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.status === 'connected') {
        return {
          qrCode: null,
          status: 'connected',
          expiresAt: null,
          lastUpdated: new Date().toISOString()
        };
      }

      if (!data.qrCode) {
        return {
          qrCode: null,
          status: 'loading',
          expiresAt: null,
          lastUpdated: new Date().toISOString()
        };
      }

      return {
        qrCode: data.qrCode,
        status: 'available',
        expiresAt: data.expiresAt || new Date(Date.now() + 45000).toISOString(),
        lastUpdated: new Date().toISOString()
      };

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // Request was aborted, don't treat as error
        throw error;
      }

      console.error('Error fetching QR code:', error);
      return {
        qrCode: null,
        status: 'error',
        expiresAt: null,
        lastUpdated: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, [instanceId]);

  /**
   * Update QR code data with status change notifications
   */
  const updateQRData = useCallback((newData: QRCodeData) => {
    setQrData(prevData => {
      // Only trigger status change if status actually changed
      if (prevData.status !== newData.status) {
        onStatusChange?.(newData.status);
        
        // Trigger specific callbacks
        if (newData.status === 'connected') {
          onConnected?.();
        } else if (newData.status === 'error' && newData.error) {
          onError?.(newData.error);
        }
      }

      return newData;
    });
  }, [onStatusChange, onConnected, onError]);

  // =====================================================
  // POLLING LOGIC
  // =====================================================

  /**
   * Start polling for QR code updates
   */
  const startPolling = useCallback(async () => {
    if (isPolling || !autoRefresh) return;

    setIsPolling(true);
    setRetryCount(0);

    const poll = async () => {
      try {
        const newQrData = await fetchQRCode();
        updateQRData(newQrData);
        setRetryCount(0); // Reset retry count on success

        // Stop polling if connected
        if (newQrData.status === 'connected') {
          setIsPolling(false);
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          return;
        }

      } catch (error) {
        if (error instanceof Error && error.name === 'AbortError') {
          return; // Ignore aborted requests
        }

        console.error('Polling error:', error);
        setRetryCount(prev => {
          const newCount = prev + 1;
          
          if (newCount >= maxRetries) {
            setIsPolling(false);
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
            updateQRData({
              qrCode: null,
              status: 'error',
              expiresAt: null,
              lastUpdated: new Date().toISOString(),
              error: `Max retries (${maxRetries}) exceeded`
            });
          }
          
          return newCount;
        });
      }
    };

    // Initial fetch
    await poll();

    // Set up interval for subsequent fetches
    if (autoRefresh && !intervalRef.current) {
      intervalRef.current = setInterval(poll, refreshInterval * 1000);
    }
  }, [isPolling, autoRefresh, fetchQRCode, updateQRData, refreshInterval, maxRetries]);

  /**
   * Stop polling
   */
  const stopPolling = useCallback(() => {
    setIsPolling(false);
    
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  /**
   * Manually refresh QR code
   */
  const refreshQRCode = useCallback(async () => {
    try {
      updateQRData({
        ...qrData,
        status: 'loading'
      });

      const newQrData = await fetchQRCode();
      updateQRData(newQrData);
      setRetryCount(0);

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }
      
      console.error('Manual refresh error:', error);
      updateQRData({
        qrCode: null,
        status: 'error',
        expiresAt: null,
        lastUpdated: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Refresh failed'
      });
    }
  }, [fetchQRCode, updateQRData, qrData]);

  // =====================================================
  // LIFECYCLE EFFECTS
  // =====================================================

  /**
   * Start polling when component mounts or instanceId changes
   */
  useEffect(() => {
    if (instanceId && autoRefresh) {
      startPolling();
    }

    return () => {
      stopPolling();
    };
  }, [instanceId, autoRefresh, startPolling, stopPolling]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      stopPolling();
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [stopPolling]);

  // =====================================================
  // RETURN HOOK INTERFACE
  // =====================================================

  return {
    // QR Code data
    qrCode: qrData.qrCode,
    status: qrData.status,
    expiresAt: qrData.expiresAt,
    lastUpdated: qrData.lastUpdated,
    error: qrData.error,

    // State flags
    isPolling,
    retryCount,

    // Control functions
    startPolling,
    stopPolling,
    refreshQRCode,

    // Computed properties
    isExpired: qrData.expiresAt ? new Date() > new Date(qrData.expiresAt) : false,
    timeUntilExpiry: qrData.expiresAt ? 
      Math.max(0, new Date(qrData.expiresAt).getTime() - Date.now()) : 0
  };
}
