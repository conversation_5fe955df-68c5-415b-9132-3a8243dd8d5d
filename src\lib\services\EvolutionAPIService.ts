/**
 * Evolution API v2 Integration Service
 * 
 * Service for managing WhatsApp Business API integration through Evolution API v2.
 * Handles instance creation, connection management, message sending, and webhook configuration.
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-28
 */

import type {
  EvolutionInstanceCreateInput,
  EvolutionWebhookConfigInput,
  EvolutionSendMessageInput
} from '@/lib/validations/whatsapp';

// =====================================================
// TYPES AND INTERFACES
// =====================================================

export interface EvolutionAPIConfig {
  baseUrl: string;
  apiKey: string;
  version: 'v2';
}

export interface EvolutionInstanceResponse {
  instance: {
    instanceName: string;
    status: string;
  };
  hash: {
    apikey: string;
  };
  webhook?: {
    url: string;
    events: string[];
  };
  qrcode?: {
    code: string;
    base64: string;
  };
}

export interface EvolutionConnectionStatus {
  instance: string;
  state: 'close' | 'connecting' | 'open';
  qr?: string;
}

export interface EvolutionMessageResponse {
  key: {
    remoteJid: string;
    fromMe: boolean;
    id: string;
  };
  message: any;
  messageTimestamp: number;
}

// =====================================================
// EVOLUTION API SERVICE CLASS
// =====================================================

/**
 * Evolution API v2 Service
 * Manages WhatsApp Business API integration through Evolution API
 */
export class EvolutionAPIService {
  private config: EvolutionAPIConfig;
  private static instance: EvolutionAPIService;

  constructor(config: EvolutionAPIConfig) {
    this.config = config;
  }

  /**
   * Get singleton instance of Evolution API Service
   */
  static getInstance(config?: EvolutionAPIConfig): EvolutionAPIService {
    if (!EvolutionAPIService.instance) {
      if (!config) {
        throw new Error('Evolution API configuration is required for first initialization');
      }
      EvolutionAPIService.instance = new EvolutionAPIService(config);
    }
    return EvolutionAPIService.instance;
  }

  /**
   * Create a new WhatsApp instance in Evolution API
   * Includes validation to prevent duplicate instances
   */
  async createInstance(data: EvolutionInstanceCreateInput): Promise<EvolutionInstanceResponse> {
    try {
      // In development mode, return mock response immediately
      const isDevelopment = process.env.NODE_ENV === 'development';
      if (isDevelopment && (!this.config.apiKey || this.config.apiKey === 'dev-api-key-placeholder')) {
        console.log('🔧 Development mode: Returning mock Evolution API response');
        return {
          instance: {
            instanceName: data.instanceName,
            instanceId: `dev-mock-${Date.now()}`,
            integration: 'WHATSAPP-BAILEYS',
            webhookWaBusiness: null,
            accessTokenWaBusiness: '',
            status: 'close'
          },
          hash: 'DEV-MOCK-HASH-' + Date.now(),
          webhook: {},
          websocket: {},
          rabbitmq: {},
          sqs: {},
          settings: {
            rejectCall: false,
            msgCall: '',
            groupsIgnore: false,
            alwaysOnline: false,
            readMessages: false,
            readStatus: false,
            syncFullHistory: false,
            wavoipToken: ''
          }
        };
      }

      // First check if instance already exists
      const existingInstances = await this.fetchAllInstances();
      const existingInstance = existingInstances.find(
        instance => instance.name === data.instanceName
      );

      if (existingInstance) {
        console.warn(`⚠️ Instance '${data.instanceName}' already exists in Evolution API`);

        // Return existing instance data in expected format
        return {
          instance: {
            instanceName: existingInstance.name,
            instanceId: existingInstance.id || `existing-${Date.now()}`,
            integration: 'WHATSAPP-BAILEYS',
            webhookWaBusiness: null,
            accessTokenWaBusiness: existingInstance.token || '',
            status: existingInstance.connectionStatus
          },
          hash: existingInstance.token || 'existing-token',
          webhook: {},
          websocket: {},
          rabbitmq: {},
          sqs: {},
          settings: {
            rejectCall: false,
            msgCall: '',
            groupsIgnore: false,
            alwaysOnline: false,
            readMessages: false,
            readStatus: false,
            syncFullHistory: false,
            wavoipToken: ''
          },
          qrcode: existingInstance.connectionStatus === 'connecting' ?
            await this.getQRCode(existingInstance.name).catch(() => ({ base64: null })) :
            { base64: null }
        };
      }

      // Prepare Evolution API v2 payload with confirmed minimal working format
      // Based on successful manual testing with https://evo.torrecentral.com
      const evolutionPayload = {
        integration: data.integration ?? 'WHATSAPP-BAILEYS', // Confirmed working integration type
        instanceName: data.instanceName,
        qrcode: true // CRITICAL: Enable QR code generation during instance creation
        // This parameter is essential for Evolution API v2 to:
        // 1. Start instance in "connecting" status instead of "close"
        // 2. Automatically generate QR codes
        // 3. Include QR code data in the creation response
      };

      console.log('📤 Evolution API payload (minimal format):', evolutionPayload);

      console.log('🔗 Creating Evolution API instance with minimal payload:', {
        instanceName: evolutionPayload.instanceName,
        integration: evolutionPayload.integration,
        endpoint: `${this.config.baseUrl}/instance/create`,
        flowType: 'qr_code_only' // Using QR code flow with WHATSAPP-BAILEYS integration
      });

      // Create new instance if it doesn't exist
      const response = await this.makeRequest('POST', '/instance/create', evolutionPayload);

      if (!response.ok) {
        const errorText = await response.text();
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { message: errorText };
        }

        console.error('❌ Evolution API create instance error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorData
        });

        throw new Error(`Evolution API error: ${errorData.message || response.statusText}`);
      }

      const result = await response.json();

      // Log the complete response structure for debugging
      console.log('✅ Evolution API instance created successfully:', {
        instanceName: result.instance?.instanceName,
        instanceId: result.instance?.instanceId, // New field from confirmed response
        integration: result.instance?.integration,
        status: result.instance?.status,
        hash: result.hash,
        hasWebhook: !!result.webhook,
        hasSettings: !!result.settings,
        // QR code data (should be available with qrcode: true parameter)
        hasQRCode: !!result.qrcode,
        qrCodeFields: result.qrcode ? Object.keys(result.qrcode) : [],
        qrCodeCount: result.qrcode?.count || 0
      });

      // Validate the response structure matches our expectations
      if (!result.instance?.instanceName || !result.instance?.instanceId) {
        console.warn('⚠️ Unexpected Evolution API response structure:', result);
      }

      // Extract QR code if available in creation response (with qrcode: true parameter)
      if (result.qrcode?.base64) {
        console.log('🎯 QR code available immediately in creation response!');
        result.qrCodeFromCreation = {
          qrcode: result.qrcode.code || '',
          base64: result.qrcode.base64,
          pairingCode: result.qrcode.pairingCode || '',
          count: result.qrcode.count || 1,
          source: 'creation_response'
        };
      }

      return result;
    } catch (error) {
      console.error('❌ Error creating Evolution API instance:', error);

      // In development mode, return mock response on error
      const isDevelopment = process.env.NODE_ENV === 'development';
      if (isDevelopment) {
        console.log('🔧 Development mode: Returning mock response due to API error');
        return {
          instance: {
            instanceName: data.instanceName,
            instanceId: `dev-error-fallback-${Date.now()}`,
            integration: 'WHATSAPP-BAILEYS',
            webhookWaBusiness: null,
            accessTokenWaBusiness: '',
            status: 'close'
          },
          hash: 'DEV-ERROR-FALLBACK-HASH-' + Date.now(),
          webhook: {},
          websocket: {},
          rabbitmq: {},
          sqs: {},
          settings: {
            rejectCall: false,
            msgCall: '',
            groupsIgnore: false,
            alwaysOnline: false,
            readMessages: false,
            readStatus: false,
            syncFullHistory: false,
            wavoipToken: ''
          }
        };
      }

      throw new Error(`Failed to create WhatsApp instance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get instance connection status
   */
  async getInstanceStatus(instanceName: string): Promise<EvolutionConnectionStatus> {
    try {
      const response = await this.makeRequest('GET', `/instance/connectionState/${instanceName}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Evolution API error: ${errorData.message || response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error getting instance status:', error);
      throw new Error(`Failed to get instance status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Connect instance to start WhatsApp connection process
   * This is required before QR codes can be generated
   */
  async connectInstance(instanceName: string): Promise<any> {
    try {
      console.log(`🔗 Connecting instance to start WhatsApp: ${instanceName}`);

      const response = await this.makeRequest('GET', `/instance/connect/${instanceName}`);

      if (!response.ok) {
        const errorText = await response.text();
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { message: errorText };
        }

        console.error('❌ Evolution API connect error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorData
        });

        throw new Error(`Evolution API connect error: ${errorData.message || response.statusText}`);
      }

      const result = await response.json();

      console.log('✅ Instance connection initiated:', {
        instanceName,
        status: result.instance?.status || 'unknown'
      });

      return result;
    } catch (error) {
      console.error('❌ Error connecting instance:', error);
      throw new Error(`Failed to connect instance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get QR code for instance connection
   * Updated to work with confirmed Evolution API v2 endpoint structure
   */
  async getQRCode(instanceName: string): Promise<{ qrcode: string; base64: string }> {
    try {
      console.log(`🔍 Getting QR code for instance: ${instanceName}`);

      // Use the confirmed working Evolution API v2 endpoint format
      const response = await this.makeRequest('GET', `/instance/qrcode/${instanceName}`);

      if (!response.ok) {
        const errorText = await response.text();
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { message: errorText };
        }

        console.error('❌ Evolution API QR code error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorData
        });

        throw new Error(`Evolution API error: ${errorData.message || response.statusText}`);
      }

      const result = await response.json();

      console.log('✅ QR code retrieved successfully:', {
        hasQRCode: !!result.qrcode,
        hasBase64: !!result.base64,
        qrCodeLength: result.qrcode?.length || 0,
        base64Length: result.base64?.length || 0
      });

      return result;
    } catch (error) {
      console.error('❌ Error getting QR code:', error);
      throw new Error(`Failed to get QR code: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Configure webhook for instance
   */
  async configureWebhook(instanceName: string, webhookConfig: any): Promise<void> {
    try {
      const response = await this.makeRequest('POST', `/webhook/set/${instanceName}`, webhookConfig);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Evolution API error: ${errorData.message || response.statusText}`);
      }
    } catch (error) {
      console.error('Error configuring webhook:', error);
      throw new Error(`Failed to configure webhook: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Send message through WhatsApp instance
   */
  async sendMessage(instanceName: string, messageData: EvolutionSendMessageInput): Promise<EvolutionMessageResponse> {
    try {
      const response = await this.makeRequest('POST', `/message/sendText/${instanceName}`, messageData);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Evolution API error: ${errorData.message || response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error(`Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete WhatsApp instance
   */
  async deleteInstance(instanceName: string): Promise<void> {
    try {
      const response = await this.makeRequest('DELETE', `/instance/delete/${instanceName}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Evolution API error: ${errorData.message || response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting instance:', error);
      throw new Error(`Failed to delete instance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Restart WhatsApp instance
   */
  async restartInstance(instanceName: string): Promise<void> {
    try {
      const response = await this.makeRequest('PUT', `/instance/restart/${instanceName}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Evolution API error: ${errorData.message || response.statusText}`);
      }
    } catch (error) {
      console.error('Error restarting instance:', error);
      throw new Error(`Failed to restart instance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get all instances from Evolution API
   */
  async fetchAllInstances(): Promise<any[]> {
    try {
      const response = await this.makeRequest('GET', '/instance/fetchInstances');

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Evolution API error: ${errorData.message || response.statusText}`);
      }

      const result = await response.json();
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error('Error fetching all instances:', error);
      throw new Error(`Failed to fetch instances: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get instance information
   */
  async getInstanceInfo(instanceName: string): Promise<any> {
    try {
      const response = await this.makeRequest('GET', `/instance/fetchInstances/${instanceName}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Evolution API error: ${errorData.message || response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error getting instance info:', error);
      throw new Error(`Failed to get instance info: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Logout from WhatsApp instance
   */
  async logoutInstance(instanceName: string): Promise<void> {
    try {
      const response = await this.makeRequest('DELETE', `/instance/logout/${instanceName}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Evolution API error: ${errorData.message || response.statusText}`);
      }
    } catch (error) {
      console.error('Error logging out instance:', error);
      throw new Error(`Failed to logout instance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // =====================================================
  // PRIVATE HELPER METHODS
  // =====================================================

  /**
   * Make HTTP request to Evolution API
   */
  private async makeRequest(method: string, endpoint: string, data?: any): Promise<Response> {
    // Normalize URL construction to avoid double slashes
    const baseUrl = this.config.baseUrl.replace(/\/+$/, ''); // Remove trailing slashes
    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    const url = `${baseUrl}${normalizedEndpoint}`;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'apikey': this.config.apiKey
    };

    const options: RequestInit = {
      method,
      headers
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }

    console.log(`🔗 Evolution API Request: ${method} ${url}`);

    return fetch(url, options);
  }

  /**
   * Validate Evolution API configuration
   */
  static validateConfig(config: EvolutionAPIConfig): boolean {
    if (!config.baseUrl || !config.apiKey) {
      return false;
    }

    try {
      new URL(config.baseUrl);
      return true;
    } catch {
      return false;
    }
  }
}

// =====================================================
// FACTORY FUNCTION
// =====================================================

/**
 * Create Evolution API Service instance with environment configuration
 */
export function createEvolutionAPIService(): EvolutionAPIService {
  const config: EvolutionAPIConfig = {
    baseUrl: process.env.EVOLUTION_API_BASE_URL || 'http://localhost:8080',
    apiKey: process.env.EVOLUTION_API_KEY || '',
    version: 'v2'
  };

  // In development mode, allow missing API key for testing
  const isDevelopment = process.env.NODE_ENV === 'development';
  const hasValidConfig = EvolutionAPIService.validateConfig(config);

  if (!hasValidConfig && !isDevelopment) {
    throw new Error('Invalid Evolution API configuration. Check EVOLUTION_API_BASE_URL and EVOLUTION_API_KEY environment variables.');
  }

  // For development, use a default API key if none provided
  if (isDevelopment && !config.apiKey) {
    config.apiKey = 'dev-api-key-placeholder';
    console.warn('⚠️ Using development mode for Evolution API - some features may not work');
  }

  return EvolutionAPIService.getInstance(config);
}

export default EvolutionAPIService;
